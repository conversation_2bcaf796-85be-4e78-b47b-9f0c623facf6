import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import requests
import json
import threading

class ClaudeChatDialog:
    def __init__(self, root):
        self.root = root
        self.root.title("Claude API Chat")
        self.root.geometry("800x600")
        self.root.configure(bg='#f0f0f0')
        
        # API configuration
        self.baseurl = "https://api.claude-Plus.top"
        self.skey = "sk-jWPEhaO8ppTod2TgEBQQM7LhJbJG8NsB9gf9Wwiz72aAbScz"
        self.model = "claude-sonnet-4-20250514"
        
        # Message history
        self.messages = [
            {
                "role": "system",
                "content": "You are a helpful assistant."
            }
        ]
        
        self.setup_ui()
        
    def setup_ui(self):
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # Title
        title_label = ttk.Label(main_frame, text="Claude API Chat", 
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, pady=(0, 10))
        
        # Chat display area
        chat_frame = ttk.LabelFrame(main_frame, text="Conversation", padding="5")
        chat_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        chat_frame.columnconfigure(0, weight=1)
        chat_frame.rowconfigure(0, weight=1)
        
        self.chat_display = scrolledtext.ScrolledText(
            chat_frame, 
            wrap=tk.WORD, 
            height=20, 
            font=('Arial', 10),
            bg='white',
            fg='black'
        )
        self.chat_display.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        self.chat_display.config(state=tk.DISABLED)
        
        # Input area
        input_frame = ttk.LabelFrame(main_frame, text="Your Message", padding="5")
        input_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        input_frame.columnconfigure(0, weight=1)
        input_frame.rowconfigure(0, weight=1)
        
        self.input_text = scrolledtext.ScrolledText(
            input_frame, 
            wrap=tk.WORD, 
            height=5, 
            font=('Arial', 10)
        )
        self.input_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 5))
        
        # Button frame
        button_frame = ttk.Frame(input_frame)
        button_frame.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        self.send_button = ttk.Button(
            button_frame, 
            text="Send", 
            command=self.send_message,
            style='Accent.TButton'
        )
        self.send_button.pack(pady=(0, 5))
        
        self.clear_button = ttk.Button(
            button_frame, 
            text="Clear", 
            command=self.clear_chat
        )
        self.clear_button.pack()
        
        # Status bar
        self.status_var = tk.StringVar()
        self.status_var.set("Ready")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, 
                              relief=tk.SUNKEN, anchor=tk.W)
        status_bar.grid(row=3, column=0, sticky=(tk.W, tk.E))
        
        # Bind Enter key to send message
        self.input_text.bind('<Control-Return>', lambda e: self.send_message())
        
        # Initial welcome message
        self.add_message_to_display("System", "Welcome to Claude API Chat! Type your message and press Send or Ctrl+Enter.", "system")
        
    def add_message_to_display(self, sender, message, msg_type="user"):
        """Add a message to the chat display"""
        self.chat_display.config(state=tk.NORMAL)
        
        # Add timestamp
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        
        # Format message based on type
        if msg_type == "system":
            self.chat_display.insert(tk.END, f"[{timestamp}] {sender}: {message}\n\n", "system")
            self.chat_display.tag_config("system", foreground="blue", font=('Arial', 10, 'italic'))
        elif msg_type == "user":
            self.chat_display.insert(tk.END, f"[{timestamp}] You: {message}\n\n", "user")
            self.chat_display.tag_config("user", foreground="green", font=('Arial', 10, 'bold'))
        elif msg_type == "assistant":
            self.chat_display.insert(tk.END, f"[{timestamp}] Claude: {message}\n\n", "assistant")
            self.chat_display.tag_config("assistant", foreground="purple", font=('Arial', 10))
        elif msg_type == "error":
            self.chat_display.insert(tk.END, f"[{timestamp}] Error: {message}\n\n", "error")
            self.chat_display.tag_config("error", foreground="red", font=('Arial', 10, 'bold'))
        
        self.chat_display.config(state=tk.DISABLED)
        self.chat_display.see(tk.END)
        
    def send_message(self):
        """Send message to Claude API"""
        user_message = self.input_text.get("1.0", tk.END).strip()
        
        if not user_message:
            messagebox.showwarning("Warning", "Please enter a message")
            return
        
        # Clear input
        self.input_text.delete("1.0", tk.END)
        
        # Add user message to display
        self.add_message_to_display("You", user_message, "user")
        
        # Add user message to history
        self.messages.append({
            "role": "user",
            "content": user_message
        })
        
        # Disable send button and update status
        self.send_button.config(state=tk.DISABLED)
        self.status_var.set("Sending message...")
        
        # Send request in a separate thread to avoid freezing UI
        thread = threading.Thread(target=self.call_api)
        thread.daemon = True
        thread.start()
        
    def call_api(self):
        """Call Claude API in a separate thread"""
        import time
        start_time = time.time()
        
        try:
            payload = json.dumps({
                "model": self.model,
                "messages": self.messages
            })
            
            url = self.baseurl + "/v1/chat/completions"
            headers = {
                'Accept': 'application/json',
                'Authorization': f'Bearer {self.skey}',
                'User-Agent': 'Claude-Chat-Dialog/1.0.0',
                'Content-Type': 'application/json'
            }
            
            # Update status to show we're waiting
            self.root.after(0, lambda: self.status_var.set("Connecting to API..."))
            
            # Increased timeout for slow responses
            response = requests.post(url, headers=headers, data=payload, timeout=60)
            response.raise_for_status()
            
            # Update status
            elapsed = time.time() - start_time
            self.root.after(0, lambda: self.status_var.set(f"Processing response... ({elapsed:.1f}s)"))
            
            data = response.json()
            
            # Debug: Log the full response for troubleshooting
            print(f"API Response (took {elapsed:.1f}s):")
            print(json.dumps(data, indent=2, ensure_ascii=False))
            
            # Extract the assistant's response - handle multiple possible formats
            assistant_message = None
            
            # Standard OpenAI format (which your API uses)
            if 'choices' in data and len(data['choices']) > 0:
                choice = data['choices'][0]
                if 'message' in choice and 'content' in choice['message']:
                    assistant_message = choice['message']['content']
            
            # Alternative: direct content field
            elif 'content' in data:
                assistant_message = data['content']
            
            # Alternative: response field
            elif 'response' in data:
                assistant_message = data['response']
            
            if assistant_message:
                # Add assistant message to history
                self.messages.append({
                    "role": "assistant",
                    "content": assistant_message
                })
                
                # Update UI in main thread
                self.root.after(0, lambda: self.handle_api_response(assistant_message, elapsed))
            else:
                error_msg = f"No valid response content found. Response keys: {list(data.keys())}"
                self.root.after(0, lambda: self.handle_api_error(error_msg))
                
        except requests.exceptions.Timeout:
            self.root.after(0, lambda: self.handle_api_error("Request timed out (60s). The API might be slow."))
        except requests.exceptions.RequestException as e:
            self.root.after(0, lambda: self.handle_api_error(f"Network error: {str(e)}"))
        except json.JSONDecodeError as e:
            self.root.after(0, lambda: self.handle_api_error(f"JSON decode error: {str(e)}"))
        except Exception as e:
            self.root.after(0, lambda: self.handle_api_error(f"Unexpected error: {str(e)}"))
            
    def handle_api_response(self, message, elapsed=0):
        """Handle successful API response"""
        self.add_message_to_display("Claude", message, "assistant")
        self.send_button.config(state=tk.NORMAL)
        self.status_var.set(f"Ready (Response took {elapsed:.1f}s)")
        
    def handle_api_error(self, error_message):
        """Handle API error"""
        self.add_message_to_display("Error", error_message, "error")
        self.send_button.config(state=tk.NORMAL)
        self.status_var.set("Error occurred")
        
    def clear_chat(self):
        """Clear the chat display and reset conversation"""
        if messagebox.askyesno("Confirm", "Clear the entire conversation?"):
            self.chat_display.config(state=tk.NORMAL)
            self.chat_display.delete("1.0", tk.END)
            self.chat_display.config(state=tk.DISABLED)
            
            # Reset message history but keep system message
            self.messages = [
                {
                    "role": "system",
                    "content": "You are a helpful assistant."
                }
            ]
            
            # Add welcome message
            self.add_message_to_display("System", "Conversation cleared. Ready for new messages!", "system")
            self.status_var.set("Ready")

def main():
    root = tk.Tk()
    
    # Set up modern theme
    style = ttk.Style()
    try:
        style.theme_use('clam')  # Modern looking theme
    except:
        pass  # Use default if clam is not available
    
    app = ClaudeChatDialog(root)
    
    # Center the window
    root.update_idletasks()
    width = root.winfo_width()
    height = root.winfo_height()
    x = (root.winfo_screenwidth() // 2) - (width // 2)
    y = (root.winfo_screenheight() // 2) - (height // 2)
    root.geometry(f'{width}x{height}+{x}+{y}')
    
    root.mainloop()

if __name__ == "__main__":
    main()
