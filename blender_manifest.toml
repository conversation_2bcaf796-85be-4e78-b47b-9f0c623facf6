schema_version = "1.0.0"

id = "blender_senpai"
version = "0.3.3"
commit = "c7824f332021c556007b313eab729c6b691b0fd8"
name = "Blender Senpai"
tagline = "<PERSON><PERSON><PERSON> (menter) in your use of Blender, using the MCP protocol"
maintainer = "<PERSON>roa<PERSON> <13391129+x<PERSON><PERSON>@users.noreply.github.com>"
type = "add-on"
platforms = ["windows-x64", "linux-x64", "macos-arm64", "macos-x64"]

blender_version_min = "4.2.0"
license = [
  "UNLICENSED",
]

wheels = ["./wheels/sse_starlette-2.3.3-py3-none-any.whl", "./wheels/multidict-6.4.3-cp311-cp311-macosx_11_0_arm64.whl", "./wheels/annotated_types-0.7.0-py3-none-any.whl", "./wheels/jaraco.functools-4.1.0-py3-none-any.whl", "./wheels/typing_extensions-4.13.2-py3-none-any.whl", "./wheels/yarl-1.20.0-cp311-cp311-macosx_11_0_arm64.whl", "./wheels/zipp-3.21.0-py3-none-any.whl", "./wheels/fastapi-0.115.12-py3-none-any.whl", "./wheels/keyring-25.6.0-py3-none-any.whl", "./wheels/numpy-1.26.4-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", "./wheels/aiofiles-24.1.0-py3-none-any.whl", "./wheels/frozenlist-1.6.0-cp311-cp311-macosx_11_0_arm64.whl", "./wheels/sniffio-1.3.1-py3-none-any.whl", "./wheels/litellm-1.67.5-py3-none-any.whl", "./wheels/pydantic_core-2.33.1-cp311-cp311-macosx_11_0_arm64.whl", "./wheels/PyYAML-6.0.2-cp311-cp311-macosx_11_0_arm64.whl", "./wheels/python_dateutil-2.9.0.post0-py2.py3-none-any.whl", "./wheels/urllib3-2.4.0-py3-none-any.whl", "./wheels/ffmpy-0.5.0-py3-none-any.whl", "./wheels/tokenizers-0.21.1-cp39-abi3-win_amd64.whl", "./wheels/propcache-0.3.1-cp311-cp311-macosx_10_9_x86_64.whl", "./wheels/httpx-0.28.1-py3-none-any.whl", "./wheels/frozenlist-1.6.0-cp311-cp311-win_amd64.whl", "./wheels/semantic_version-2.10.0-py2.py3-none-any.whl", "./wheels/MarkupSafe-3.0.2-cp311-cp311-macosx_10_9_universal2.whl", "./wheels/pydantic_core-2.33.1-cp311-cp311-macosx_10_12_x86_64.whl", "./wheels/anyio-4.9.0-py3-none-any.whl", "./wheels/rich-14.0.0-py3-none-any.whl", "./wheels/more_itertools-10.7.0-py3-none-any.whl", "./wheels/pillow-11.2.1-cp311-cp311-macosx_11_0_arm64.whl", "./wheels/yarl-1.20.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", "./wheels/pandas-2.2.3-cp311-cp311-macosx_11_0_arm64.whl", "./wheels/rpds_py-0.24.0-cp311-cp311-win_amd64.whl", "./wheels/orjson-3.10.18-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", "./wheels/jaraco.classes-3.4.0-py3-none-any.whl", "./wheels/click-8.1.8-py3-none-any.whl", "./wheels/numpy-1.26.4-cp311-cp311-macosx_11_0_arm64.whl", "./wheels/huggingface_hub-0.30.2-py3-none-any.whl", "./wheels/jaraco.context-6.0.1-py3-none-any.whl", "./wheels/MarkupSafe-3.0.2-cp311-cp311-macosx_11_0_arm64.whl", "./wheels/jiter-0.9.0-cp311-cp311-macosx_11_0_arm64.whl", "./wheels/tzdata-2025.2-py2.py3-none-any.whl", "./wheels/propcache-0.3.1-cp311-cp311-macosx_11_0_arm64.whl", "./wheels/pillow-11.2.1-cp311-cp311-macosx_10_10_x86_64.whl", "./wheels/jiter-0.9.0-cp311-cp311-macosx_10_12_x86_64.whl", "./wheels/typing_inspection-0.4.0-py3-none-any.whl", "./wheels/mdurl-0.1.2-py3-none-any.whl", "./wheels/PyYAML-6.0.2-cp311-cp311-win_amd64.whl", "./wheels/attrs-25.3.0-py3-none-any.whl", "./wheels/orjson-3.10.18-cp311-cp311-win_amd64.whl", "./wheels/gradio-5.29.0-py3-none-any.whl", "./wheels/pywin32-310-cp311-cp311-win_amd64.whl", "./wheels/tiktoken-0.9.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", "./wheels/propcache-0.3.1-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", "./wheels/pydantic_settings-2.9.1-py3-none-any.whl", "./wheels/pillow-11.2.1-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", "./wheels/shellingham-1.5.4-py2.py3-none-any.whl", "./wheels/frozenlist-1.6.0-cp311-cp311-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl", "./wheels/httpx_sse-0.4.0-py3-none-any.whl", "./wheels/distro-1.9.0-py3-none-any.whl", "./wheels/aiosignal-1.3.2-py2.py3-none-any.whl", "./wheels/groovy-0.1.2-py3-none-any.whl", "./wheels/rpds_py-0.24.0-cp311-cp311-macosx_10_12_x86_64.whl", "./wheels/packaging-25.0-py3-none-any.whl", "./wheels/charset_normalizer-3.4.1-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", "./wheels/pandas-2.2.3-cp311-cp311-macosx_10_9_x86_64.whl", "./wheels/PyYAML-6.0.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", "./wheels/pydantic-2.11.3-py3-none-any.whl", "./wheels/python_multipart-0.0.20-py3-none-any.whl", "./wheels/pygments-2.19.1-py3-none-any.whl", "./wheels/regex-2024.11.6-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", "./wheels/openai-1.76.2-py3-none-any.whl", "./wheels/multidict-6.4.3-cp311-cp311-macosx_10_9_x86_64.whl", "./wheels/tokenizers-0.21.1-cp39-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", "./wheels/tiktoken-0.9.0-cp311-cp311-macosx_11_0_arm64.whl", "./wheels/referencing-0.36.2-py3-none-any.whl", "./wheels/aiohttp-3.11.18-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", "./wheels/aiohappyeyeballs-2.6.1-py3-none-any.whl", "./wheels/requests-2.32.3-py3-none-any.whl", "./wheels/tiktoken-0.9.0-cp311-cp311-macosx_10_12_x86_64.whl", "./wheels/charset_normalizer-3.4.1-cp311-cp311-macosx_10_9_universal2.whl", "./wheels/jsonschema-4.23.0-py3-none-any.whl", "./wheels/jsonschema_specifications-2025.4.1-py3-none-any.whl", "./wheels/pandas-2.2.3-cp311-cp311-win_amd64.whl", "./wheels/certifi-2025.1.31-py3-none-any.whl", "./wheels/httpcore-1.0.8-py3-none-any.whl", "./wheels/regex-2024.11.6-cp311-cp311-macosx_11_0_arm64.whl", "./wheels/multidict-6.4.3-cp311-cp311-win_amd64.whl", "./wheels/MarkupSafe-3.0.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", "./wheels/websockets-15.0.1-cp311-cp311-macosx_10_9_x86_64.whl", "./wheels/pydub-0.25.1-py2.py3-none-any.whl", "./wheels/multidict-6.4.3-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", "./wheels/six-1.17.0-py2.py3-none-any.whl", "./wheels/charset_normalizer-3.4.1-cp311-cp311-win_amd64.whl", "./wheels/rpds_py-0.24.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", "./wheels/importlib_metadata-8.7.0-py3-none-any.whl", "./wheels/safehttpx-0.1.6-py3-none-any.whl", "./wheels/aiohttp-3.11.18-cp311-cp311-macosx_10_9_x86_64.whl", "./wheels/h11-0.14.0-py3-none-any.whl", "./wheels/PyYAML-6.0.2-cp311-cp311-macosx_10_9_x86_64.whl", "./wheels/orjson-3.10.18-cp311-cp311-macosx_10_15_x86_64.macosx_11_0_arm64.macosx_10_15_universal2.whl", "./wheels/mcp-1.7.1-py3-none-any.whl", "./wheels/tokenizers-0.21.1-cp39-abi3-macosx_11_0_arm64.whl", "./wheels/fsspec-2025.3.2-py3-none-any.whl", "./wheels/uvicorn-0.34.2-py3-none-any.whl", "./wheels/websockets-15.0.1-cp311-cp311-macosx_11_0_arm64.whl", "./wheels/frozenlist-1.6.0-cp311-cp311-macosx_10_9_x86_64.whl", "./wheels/regex-2024.11.6-cp311-cp311-macosx_10_9_x86_64.whl", "./wheels/backports.tarfile-1.2.0-py3-none-any.whl", "./wheels/pywin32_ctypes-0.2.3-py3-none-any.whl", "./wheels/websockets-15.0.1-cp311-cp311-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl", "./wheels/starlette-0.46.2-py3-none-any.whl", "./wheels/tokenizers-0.21.1-cp39-abi3-macosx_10_12_x86_64.whl", "./wheels/aiohttp-3.11.18-cp311-cp311-win_amd64.whl", "./wheels/websockets-15.0.1-cp311-cp311-win_amd64.whl", "./wheels/gradio_client-1.10.0-py3-none-any.whl", "./wheels/filelock-3.18.0-py3-none-any.whl", "./wheels/python_dotenv-1.1.0-py3-none-any.whl", "./wheels/jiter-0.9.0-cp311-cp311-win_amd64.whl", "./wheels/tqdm-4.67.1-py3-none-any.whl", "./wheels/idna-3.10-py3-none-any.whl", "./wheels/numpy-1.26.4-cp311-cp311-macosx_10_9_x86_64.whl", "./wheels/regex-2024.11.6-cp311-cp311-win_amd64.whl", "./wheels/markdown_it_py-3.0.0-py3-none-any.whl", "./wheels/rpds_py-0.24.0-cp311-cp311-macosx_11_0_arm64.whl", "./wheels/aiohttp-3.11.18-cp311-cp311-macosx_11_0_arm64.whl", "./wheels/pandas-2.2.3-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", "./wheels/tomlkit-0.13.2-py3-none-any.whl", "./wheels/yarl-1.20.0-cp311-cp311-win_amd64.whl", "./wheels/jiter-0.9.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", "./wheels/propcache-0.3.1-cp311-cp311-win_amd64.whl", "./wheels/pytz-2025.2-py2.py3-none-any.whl", "./wheels/pillow-11.2.1-cp311-cp311-win_amd64.whl", "./wheels/typer-0.15.3-py3-none-any.whl", "./wheels/numpy-1.26.4-cp311-cp311-win_amd64.whl", "./wheels/pydantic_core-2.33.1-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", "./wheels/jinja2-3.1.6-py3-none-any.whl", "./wheels/pydantic_core-2.33.1-cp311-cp311-win_amd64.whl", "./wheels/yarl-1.20.0-cp311-cp311-macosx_10_9_x86_64.whl", "./wheels/MarkupSafe-3.0.2-cp311-cp311-win_amd64.whl", "./wheels/tiktoken-0.9.0-cp311-cp311-win_amd64.whl"]


# BEGIN GENERATED CONTENT.
# This must not be included in source manifests.
[build.generated]
platforms = ["windows-x64"]
wheels = ["./wheels/sse_starlette-2.3.3-py3-none-any.whl", "./wheels/annotated_types-0.7.0-py3-none-any.whl", "./wheels/jaraco.functools-4.1.0-py3-none-any.whl", "./wheels/typing_extensions-4.13.2-py3-none-any.whl", "./wheels/zipp-3.21.0-py3-none-any.whl", "./wheels/fastapi-0.115.12-py3-none-any.whl", "./wheels/keyring-25.6.0-py3-none-any.whl", "./wheels/aiofiles-24.1.0-py3-none-any.whl", "./wheels/sniffio-1.3.1-py3-none-any.whl", "./wheels/litellm-1.67.5-py3-none-any.whl", "./wheels/python_dateutil-2.9.0.post0-py2.py3-none-any.whl", "./wheels/urllib3-2.4.0-py3-none-any.whl", "./wheels/ffmpy-0.5.0-py3-none-any.whl", "./wheels/tokenizers-0.21.1-cp39-abi3-win_amd64.whl", "./wheels/httpx-0.28.1-py3-none-any.whl", "./wheels/frozenlist-1.6.0-cp311-cp311-win_amd64.whl", "./wheels/semantic_version-2.10.0-py2.py3-none-any.whl", "./wheels/anyio-4.9.0-py3-none-any.whl", "./wheels/rich-14.0.0-py3-none-any.whl", "./wheels/more_itertools-10.7.0-py3-none-any.whl", "./wheels/rpds_py-0.24.0-cp311-cp311-win_amd64.whl", "./wheels/jaraco.classes-3.4.0-py3-none-any.whl", "./wheels/click-8.1.8-py3-none-any.whl", "./wheels/huggingface_hub-0.30.2-py3-none-any.whl", "./wheels/jaraco.context-6.0.1-py3-none-any.whl", "./wheels/tzdata-2025.2-py2.py3-none-any.whl", "./wheels/typing_inspection-0.4.0-py3-none-any.whl", "./wheels/mdurl-0.1.2-py3-none-any.whl", "./wheels/PyYAML-6.0.2-cp311-cp311-win_amd64.whl", "./wheels/attrs-25.3.0-py3-none-any.whl", "./wheels/orjson-3.10.18-cp311-cp311-win_amd64.whl", "./wheels/gradio-5.29.0-py3-none-any.whl", "./wheels/pywin32-310-cp311-cp311-win_amd64.whl", "./wheels/pydantic_settings-2.9.1-py3-none-any.whl", "./wheels/shellingham-1.5.4-py2.py3-none-any.whl", "./wheels/httpx_sse-0.4.0-py3-none-any.whl", "./wheels/distro-1.9.0-py3-none-any.whl", "./wheels/aiosignal-1.3.2-py2.py3-none-any.whl", "./wheels/groovy-0.1.2-py3-none-any.whl", "./wheels/packaging-25.0-py3-none-any.whl", "./wheels/pydantic-2.11.3-py3-none-any.whl", "./wheels/python_multipart-0.0.20-py3-none-any.whl", "./wheels/pygments-2.19.1-py3-none-any.whl", "./wheels/openai-1.76.2-py3-none-any.whl", "./wheels/referencing-0.36.2-py3-none-any.whl", "./wheels/aiohappyeyeballs-2.6.1-py3-none-any.whl", "./wheels/requests-2.32.3-py3-none-any.whl", "./wheels/jsonschema-4.23.0-py3-none-any.whl", "./wheels/jsonschema_specifications-2025.4.1-py3-none-any.whl", "./wheels/pandas-2.2.3-cp311-cp311-win_amd64.whl", "./wheels/certifi-2025.1.31-py3-none-any.whl", "./wheels/httpcore-1.0.8-py3-none-any.whl", "./wheels/multidict-6.4.3-cp311-cp311-win_amd64.whl", "./wheels/pydub-0.25.1-py2.py3-none-any.whl", "./wheels/six-1.17.0-py2.py3-none-any.whl", "./wheels/charset_normalizer-3.4.1-cp311-cp311-win_amd64.whl", "./wheels/importlib_metadata-8.7.0-py3-none-any.whl", "./wheels/safehttpx-0.1.6-py3-none-any.whl", "./wheels/h11-0.14.0-py3-none-any.whl", "./wheels/mcp-1.7.1-py3-none-any.whl", "./wheels/fsspec-2025.3.2-py3-none-any.whl", "./wheels/uvicorn-0.34.2-py3-none-any.whl", "./wheels/backports.tarfile-1.2.0-py3-none-any.whl", "./wheels/pywin32_ctypes-0.2.3-py3-none-any.whl", "./wheels/starlette-0.46.2-py3-none-any.whl", "./wheels/aiohttp-3.11.18-cp311-cp311-win_amd64.whl", "./wheels/websockets-15.0.1-cp311-cp311-win_amd64.whl", "./wheels/gradio_client-1.10.0-py3-none-any.whl", "./wheels/filelock-3.18.0-py3-none-any.whl", "./wheels/python_dotenv-1.1.0-py3-none-any.whl", "./wheels/jiter-0.9.0-cp311-cp311-win_amd64.whl", "./wheels/tqdm-4.67.1-py3-none-any.whl", "./wheels/idna-3.10-py3-none-any.whl", "./wheels/regex-2024.11.6-cp311-cp311-win_amd64.whl", "./wheels/markdown_it_py-3.0.0-py3-none-any.whl", "./wheels/tomlkit-0.13.2-py3-none-any.whl", "./wheels/yarl-1.20.0-cp311-cp311-win_amd64.whl", "./wheels/propcache-0.3.1-cp311-cp311-win_amd64.whl", "./wheels/pytz-2025.2-py2.py3-none-any.whl", "./wheels/pillow-11.2.1-cp311-cp311-win_amd64.whl", "./wheels/typer-0.15.3-py3-none-any.whl", "./wheels/numpy-1.26.4-cp311-cp311-win_amd64.whl", "./wheels/jinja2-3.1.6-py3-none-any.whl", "./wheels/pydantic_core-2.33.1-cp311-cp311-win_amd64.whl", "./wheels/MarkupSafe-3.0.2-cp311-cp311-win_amd64.whl", "./wheels/tiktoken-0.9.0-cp311-cp311-win_amd64.whl"]
# END GENERATED CONTENT.
